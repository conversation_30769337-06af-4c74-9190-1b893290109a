<template>
  <nav class="bottom-nav">
    <a
        v-for="(item, index) in navItems"
        :key="index"
        href="#"
        :class="{ active: index === props.activeIndex }"
        @click="handleClick(index, $event)"
    >
      <div class="icon-label-wrapper">
        <i class="fas" :class="item.iconClass"></i>
        <span class="label">{{ item.label }}</span>
      </div>
    </a>
  </nav>
</template>

<script setup>
import { onMounted, ref } from "vue";

onMounted(() => {
  loadFontAwesome();
});

const props = defineProps({
  navItems: Array,
  activeIndex: Number,
})

const emit = defineEmits(["navigate"]);

const handleClick = (index, e) => {
  e.preventDefault();
  emit("navigate", props.navItems[index].routeName);
};

const loadFontAwesome = () => {
  // 动态引入Font Awesome（与首页保持一致）
  if (!document.getElementById("fa-mobile-page")) {
    const link = document.createElement("link");
    link.id = "fa-mobile-page";
    link.rel = "stylesheet";
    link.href =
        "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css";
    document.head.appendChild(link);
  }
};
</script>

<style scoped>
@import url("https://fonts.googleapis.com/css2?family=PT+Sans+Narrow&display=swap");

.bottom-nav {
  position: fixed;
  bottom: 2%;
  width: 75%;
  height: 7%;
  left: 50%;
  border: 1px solid #252e45;
  background-color: #6789de8e;
  /* background-color: #1a20308d; */
  transform: translateX(-50%);
  border-radius: 100px;
  display: flex;
  flex-wrap: wrap;
  z-index: 999;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.5);
}

.bottom-nav a {
  border-radius: 100px;
  width: 33.33%;
  padding-top: 1.8%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: "ff-chambers-sans-web", sans-serif;
  font-weight: 500;
  text-decoration: none;
  position: relative;
  overflow: hidden;
  flex-direction: column;
}

.bottom-nav a .icon-label-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
  height: 4rem;
  width: 4rem;
  overflow: hidden;
  border-radius: 50%;
  z-index: 2;
}

.bottom-nav a .fas {
  font-size: 1.8rem;
  margin-top: 1rem;
  color: #fff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  display: inline-block;
  transition: transform 0.3s ease;
}

.bottom-nav a .label {
  font-family: "PT Sans Narrow", serif;
  font-size: 0.9rem;
  opacity: 0;
  transition: opacity 0.3s ease;
  margin-top: 0.2rem;
  color: inherit;
  text-align: center;
  z-index: 999;
}

.bottom-nav a.active .icon-label-wrapper {
  background-color: #fff;
  transform: translateY(-6px);
  color: #000;
}

.bottom-nav > a.active .fas {
  color: #44598b !important;
  margin-top: 0.2rem;
}

.bottom-nav a.active .label {
  opacity: 1;
}
</style>
