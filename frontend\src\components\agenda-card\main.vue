<template>
  <div id="timeline-content">
    <ul class="timeline">
      <li class="event" v-for="(item, index) in events" :key="index" :data-date="item.time">
        <div class="event-main">
          <div class="event-left">
            <h3>{{ item.title }}</h3>
            <p>{{ item.description }}</p>
          </div>

          <div class="event-right">
            <div class="meta-item">
              <i class="fas fa-user"></i>
              <span>{{ item.host }}</span>
            </div>
            <div class="meta-item">
              <i class="fas fa-map-marker-alt"></i>
              <span>{{ item.location }}</span>
            </div>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script setup>
onMounted(() => {
  if (!document.getElementById("fa-timeline")) {
    const link = document.createElement("link");
    link.id = "fa-timeline";
    link.rel = "stylesheet";
    link.href =
      "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css";
    document.head.appendChild(link);
  }
});

const events = [
  {
    title: "签到注册",
    description: "参会者签到，领取会议资料",
    host: "会务组",
    location: "主会场大厅",
    time: "09:00 - 10:00",
  },
];
</script>

<style lang="scss">
@use "sass:color";
@import url("https://fonts.googleapis.com/css?family=Chivo:300,300i,400,400i,700,700i,900,900i|Saira+Extra+Condensed:100,200,300,400,500,600,700,800|Saira:100,200,300,400,500,600,700,800");

$background: #252827;
$color-primary: #004ffc;
$color-light: white;
$color-dim: #6c6d6d;
$spacing: 20px;
$radius: 4px;

$date: 120px;
$dotborder: 4px;
$dot: 11px;
$line: 4px;

$font-title: "Saira", sans-serif;
$font-text: "Chivo", sans-serif;

body {
  background: $background;
  font-size: 16px;
  font-family: $font-text;
}

#timeline-content {
  text-align: center;
  color: $color-light;
}

.timeline {
  border-left: $line solid $color-primary;
  border-bottom-right-radius: $radius;
  border-top-right-radius: $radius;
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  color: rgba(255, 255, 255, 0.8);
  letter-spacing: 0.5px;
  position: relative;
  line-height: 1.4em;
  font-size: 1.03em;
  padding: $spacing * 0.8;
  list-style: none;
  text-align: left;
  font-weight: 100;
  max-width: 90%;

  h1,
  h2,
  h3 {
    font-family: $font-title;
    letter-spacing: 1.5px;
    font-weight: 400;
  }

  h1 {
    font-size: 1.4em;
    font-weight: 100;
  }

  h3 {
    font-size: 1.4em;
    padding-top: 2%;
  }

  .event {
    border-bottom: 1px dashed rgba(255, 255, 255, 0.1);
    padding-bottom: ($spacing * 0.5);
    margin-bottom: $spacing;
    position: relative;

    &:last-of-type {
      padding-bottom: 0;
      margin-bottom: 0;
      border: none;
    }

    &:before,
    &:after {
      position: absolute;
      display: block;
      top: 0;
    }

    &:before {
      left: -0.1rem;
      color: rgba(255, 255, 255, 0.85);
      content: attr(data-date);
      text-align: right;
      font-weight: 300;
      font-size: 1.2em;
      min-width: $date;
      font-family: $font-title;
    }

    &:after {
      box-shadow: 0 0 0 $dotborder $color-primary;
      left: ($spacing + $line + ($dot * 0.35)) * -1;
      background: color.scale($background, $lightness: 5.8891454965%);
      border-radius: 50%;
      height: $dot;
      width: $dot;
      content: "";
      top: 5px;
    }
  }
}

.event-main {
  display: flex;
  justify-content: space-between;
  flex-wrap: nowrap;
  width: 100%;
}

.event-content {
  flex: 1;
  width: 100%;
}

.event-meta {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
  text-align: right;
  font-size: 0.95em;
  color: rgba(255, 255, 255, 0.85);
  gap: 5px;
}

.event-left {
  flex: 0 0 65%;
  min-width: 0;
}

.event-right {
  flex: 0 0 35%;
  display: flex;
  flex-direction: column;
  text-align: right;
  gap: 10%;
  padding-left: 5%;
  padding-top: 0;
  justify-content: right;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.95em;
  color: rgba(255, 255, 255, 0.85);

  i {
    font-size: 1em;
    color: #ccc;
  }
}

.meta-icon {
  font-size: 1.1em;
}
</style>
