<template>
  <div class="qr-manager-content">
    <!-- 页面标题 -->
    <div class="page-header list-item">
      <div class="header-icon">
        <i class="fas fa-qrcode"></i>
      </div>
      <h2>签到二维码管理</h2>
      <p class="header-description">生成和管理会议议程的签到二维码</p>
    </div>

    <!-- 议程选择 -->
    <div class="agenda-selector-container list-item">
      <div class="selector-header">
        <h3>选择议程</h3>
        <p>请选择要生成签到二维码的议程</p>
      </div>

      <div class="agenda-selector">
        <select
          v-model="selectedAgendaId"
          @change="onAgendaChange"
          class="agenda-select"
          :disabled="loading"
        >
          <option value="">请选择议程</option>
          <option
            v-for="agenda in agendaList"
            :key="agenda.id"
            :value="agenda.id"
          >
            {{ agenda.topic }} - {{ formatTime(agenda.startTime) }}
          </option>
        </select>

        <div v-if="loading" class="loading-indicator">
          <i class="fas fa-spinner fa-spin"></i>
          <span>加载议程中...</span>
        </div>

        <div v-else-if="agendaList.length === 0" class="empty-indicator">
          <i class="fas fa-calendar-times"></i>
          <span>暂无议程</span>
        </div>
      </div>

      <!-- 生成按钮 -->
      <div class="generate-section" v-if="selectedAgenda">
        <div class="selected-agenda-info">
          <h4>{{ selectedAgenda.topic }}</h4>
          <p class="agenda-time">
            <i class="fas fa-clock"></i>
            {{ formatTime(selectedAgenda.startTime) }} - {{ formatTime(selectedAgenda.endTime) }}
          </p>
          <p class="agenda-venue" v-if="selectedAgenda.venue">
            <i class="fas fa-map-marker-alt"></i>
            {{ selectedAgenda.venue }}
          </p>
        </div>
        <button class="generate-btn" @click="generateQRCode(selectedAgenda)">
          <i class="fas fa-qrcode"></i>
          生成签到二维码
        </button>
      </div>
    </div>

    <!-- 二维码显示弹窗 -->
    <div v-if="showQRModal" class="qr-modal" @click="closeQRModal">
      <div class="qr-modal-content" @click.stop>
        <div class="qr-modal-header">
          <h3>{{ selectedAgenda?.topic }}</h3>
          <button class="close-btn" @click="closeQRModal">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div class="qr-modal-body">
          <div v-if="qrCodeLoading" class="qr-loading">
            <i class="fas fa-spinner fa-spin"></i>
            <p>正在生成二维码...</p>
          </div>
          
          <div v-else-if="qrCodeData" class="qr-display">
            <div class="qr-image-container">
              <img :src="qrCodeData" alt="签到二维码" class="qr-image" />
            </div>
            <div class="qr-info">
              <p class="qr-tips">请将此二维码展示在会议现场供参会人员扫码签到</p>
              <div class="qr-actions">
                <button class="action-btn refresh-btn" @click="generateQRCode(selectedAgenda)">
                  <i class="fas fa-refresh"></i>
                  刷新二维码
                </button>
                <button class="action-btn download-btn" @click="downloadQRCode">
                  <i class="fas fa-download"></i>
                  下载二维码
                </button>
              </div>
            </div>
          </div>
          
          <div v-else class="qr-error">
            <i class="fas fa-exclamation-triangle"></i>
            <p>二维码生成失败</p>
            <button class="retry-btn" @click="generateQRCode(selectedAgenda)">重试</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import request from '@/axios'

export default {
  name: 'QRCodeManager',
  data() {
    return {
      loading: false,
      agendaList: [],
      selectedAgendaId: '',
      showQRModal: false,
      selectedAgenda: null,
      qrCodeLoading: false,
      qrCodeData: '',
      checkinCode: ''
    }
  },
  mounted() {
    // 二维码管理页面不需要登录验证，直接加载议程列表
    this.loadAgendaList()
  },
  methods: {
    goBack() {
      this.$parent.goBack()
    },

    // 加载议程列表
    async loadAgendaList() {
      this.loading = true
      try {
        // 议程列表是公开接口，不需要token
        const response = await request({
          method: 'get',
          url: '/api/hy/agenda/list',
          meta: { isToken: false }
        })
        if (response.data.code === 200) {
          this.agendaList = response.data.data.records || []
        }
      } catch (error) {
        console.error('加载议程列表失败:', error)
        this.$message && this.$message.error 
          ? this.$message.error('加载议程列表失败') 
          : alert('加载议程列表失败')
      } finally {
        this.loading = false
      }
    },

    // 议程选择变化
    onAgendaChange() {
      if (this.selectedAgendaId) {
        this.selectedAgenda = this.agendaList.find(agenda => agenda.id == this.selectedAgendaId)
      } else {
        this.selectedAgenda = null
      }
    },

    // 生成二维码
    async generateQRCode(agenda) {
      // 二维码生成不需要用户登录，移除登录检查

      this.selectedAgenda = agenda
      this.showQRModal = true
      this.qrCodeLoading = true
      this.qrCodeData = ''

      try {
        // 二维码生成接口不需要认证，设置isToken为false避免401跳转
        const response = await request({
          method: 'get',
          url: `/api/attendance/agenda/${agenda.id}/qrcode`,
          meta: { isToken: false }
        })
        if (response.data.code === 200) {
          const data = response.data.data
          this.qrCodeData = data.qrCodeData
          this.checkinCode = data.checkinCode
        } else {
          this.$message && this.$message.error
            ? this.$message.error('生成二维码失败')
            : alert('生成二维码失败')
        }
      } catch (error) {
        console.error('生成二维码失败:', error)
        this.$message && this.$message.error
          ? this.$message.error(error.message || '生成二维码失败')
          : alert(error.message || '生成二维码失败')
      } finally {
        this.qrCodeLoading = false
      }
    },

    // 关闭二维码弹窗
    closeQRModal() {
      this.showQRModal = false
      this.selectedAgenda = null
      this.qrCodeData = ''
      this.checkinCode = ''
    },

    // 下载二维码
    downloadQRCode() {
      if (!this.qrCodeData) return
      
      const link = document.createElement('a')
      link.href = this.qrCodeData
      link.download = `${this.selectedAgenda.topic}_签到二维码.png`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },

    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return ''
      const date = new Date(timeStr)
      return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  }
}
</script>

<style scoped>
/* 页面容器 */
.qr-manager-content {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

/* 页面标题 */
.page-header {
  text-align: center;
  padding: 30px 20px;
  margin-bottom: 20px;
}

.header-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #07D3F0 0%, #0BB5D1 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
}

.header-icon i {
  font-size: 28px;
  color: white;
}

.page-header h2 {
  color: #ffffff;
  font-size: 24px;
  margin: 0 0 10px 0;
}

.header-description {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  margin: 0;
}

/* 议程选择器容器 */
.agenda-selector-container {
  padding: 25px;
  margin-bottom: 20px;
}

.selector-header {
  text-align: center;
  margin-bottom: 25px;
}

.selector-header h3 {
  color: #ffffff;
  font-size: 20px;
  margin: 0 0 8px 0;
}

.selector-header p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  margin: 0;
}

/* 议程选择器 */
.agenda-selector {
  margin-bottom: 25px;
}

.agenda-select {
  width: 100%;
  padding: 15px 20px;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(7, 211, 240, 0.3);
  border-radius: 12px;
  color: #ffffff;
  font-size: 16px;
  outline: none;
  transition: all 0.3s ease;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='rgba(255,255,255,0.6)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 15px center;
  background-size: 20px;
  padding-right: 50px;
}

.agenda-select:focus {
  border-color: rgba(7, 211, 240, 0.6);
  background: rgba(255, 255, 255, 0.12);
  box-shadow: 0 0 0 3px rgba(7, 211, 240, 0.1);
}

.agenda-select:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.agenda-select option {
  background: #1a1a2e;
  color: #ffffff;
  padding: 10px;
}

/* 加载和空状态指示器 */
.loading-indicator,
.empty-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 15px;
  margin-top: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
}

.loading-indicator i {
  color: #07D3F0;
}

.empty-indicator i {
  color: rgba(255, 255, 255, 0.3);
}

/* 生成区域 */
.generate-section {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 25px;
}

.selected-agenda-info {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
}

.selected-agenda-info h4 {
  color: #ffffff;
  font-size: 18px;
  margin: 0 0 12px 0;
}

.agenda-time,
.agenda-venue {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  margin: 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.generate-btn {
  background: linear-gradient(135deg, #07D3F0 0%, #0BB5D1 100%);
  color: white;
  border: none;
  border-radius: 25px;
  padding: 15px 30px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  width: 100%;
  box-shadow: 0 4px 15px rgba(7, 211, 240, 0.2);
}

.generate-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(7, 211, 240, 0.4);
  background: linear-gradient(135deg, #0BB5D1 0%, #07D3F0 100%);
}

.generate-btn:active {
  transform: translateY(0);
  box-shadow: 0 4px 15px rgba(7, 211, 240, 0.2);
}

/* 二维码弹窗 */
.qr-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.qr-modal-content {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-radius: 15px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.qr-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 25px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.qr-modal-header h3 {
  color: #ffffff;
  font-size: 18px;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  font-size: 20px;
  cursor: pointer;
  padding: 5px;
  transition: color 0.3s ease;
}

.close-btn:hover {
  color: #ffffff;
}

.qr-modal-body {
  padding: 30px 25px;
}

/* 二维码显示 */
.qr-loading {
  text-align: center;
  padding: 40px 20px;
}

.qr-loading i {
  font-size: 32px;
  color: #07D3F0;
  margin-bottom: 15px;
}

.qr-loading p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
  margin: 0;
}

.qr-display {
  text-align: center;
}

.qr-image-container {
  background: white;
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 25px;
  display: inline-block;
}

.qr-image {
  width: 250px;
  height: 250px;
  display: block;
}

.qr-tips {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  margin: 0 0 25px 0;
  line-height: 1.5;
}

.qr-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.action-btn {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  padding: 10px 20px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.refresh-btn:hover {
  background: rgba(7, 211, 240, 0.2);
  border-color: rgba(7, 211, 240, 0.3);
  color: #07D3F0;
}

.download-btn:hover {
  background: rgba(76, 175, 80, 0.2);
  border-color: rgba(76, 175, 80, 0.3);
  color: #4CAF50;
}

.qr-error {
  text-align: center;
  padding: 40px 20px;
}

.qr-error i {
  font-size: 32px;
  color: #e74c3c;
  margin-bottom: 15px;
}

.qr-error p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
  margin: 0 0 20px 0;
}

.retry-btn {
  background: rgba(231, 76, 60, 0.2);
  color: #e74c3c;
  border: 1px solid rgba(231, 76, 60, 0.3);
  border-radius: 25px;
  padding: 10px 20px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: rgba(231, 76, 60, 0.3);
  transform: translateY(-1px);
}
</style>
