-- 2025-07-29 用户日程信息表
-- 用于存储用户的日程、用餐、住宿信息，给个人中心使用

DROP TABLE IF EXISTS "hy_user_schedule";
CREATE TABLE hy_user_schedule (
    id BIGINT PRIMARY KEY,                    -- 主键，自增
    user_id BIGINT NOT NULL,                  -- 用户ID，关联blade_user表
    schedule_content TEXT,                    -- 日程信息（富文本）
    dining_info TEXT,                        -- 用餐信息（JSON格式）
    accommodation_info TEXT,                 -- 住宿信息（JSON格式）
    create_user BIGINT,                       -- 创建人
    create_dept BIGINT,                       -- 创建部门
    create_time TIMESTAMP(6),                 -- 创建时间
    update_user BIGINT,                       -- 更新人
    update_time TIMESTAMP(6),                 -- 更新时间
    status INT DEFAULT 1,                     -- 状态
    is_deleted INT DEFAULT 0                  -- 是否删除
);

-- 添加表注释
COMMENT ON TABLE hy_user_schedule IS '用户日程信息表';
COMMENT ON COLUMN hy_user_schedule.id IS '主键，自增';
COMMENT ON COLUMN hy_user_schedule.user_id IS '用户ID，关联blade_user表';
COMMENT ON COLUMN hy_user_schedule.schedule_content IS '日程信息（富文本）';
COMMENT ON COLUMN hy_user_schedule.dining_info IS '用餐信息（JSON格式）';
COMMENT ON COLUMN hy_user_schedule.accommodation_info IS '住宿信息（JSON格式）';
COMMENT ON COLUMN hy_user_schedule.create_user IS '创建人';
COMMENT ON COLUMN hy_user_schedule.create_dept IS '创建部门';
COMMENT ON COLUMN hy_user_schedule.create_time IS '创建时间';
COMMENT ON COLUMN hy_user_schedule.update_user IS '更新人';
COMMENT ON COLUMN hy_user_schedule.update_time IS '更新时间';
COMMENT ON COLUMN hy_user_schedule.status IS '状态';
COMMENT ON COLUMN hy_user_schedule.is_deleted IS '是否删除';



-- 创建索引
CREATE INDEX idx_hy_user_schedule_user_id ON hy_user_schedule(user_id);
CREATE INDEX idx_hy_user_schedule_status ON hy_user_schedule(status);
CREATE INDEX idx_hy_user_schedule_is_deleted ON hy_user_schedule(is_deleted);

-- 2025-07-30 为hy_sub_venue表添加视频URL和PDF URL字段
ALTER TABLE hy_sub_venue
    ADD COLUMN video_url VARCHAR(500),
    ADD COLUMN pdf_url VARCHAR(500);

COMMENT ON COLUMN hy_sub_venue.video_url IS '视频URL';
COMMENT ON COLUMN hy_sub_venue.pdf_url IS 'PDF文档URL';

-- 2025-08-01 为blade_user表添加工号、房号、会议座位号字段
ALTER TABLE blade_user
    ADD COLUMN employee_number VARCHAR(50),
    ADD COLUMN room_number VARCHAR(50),
    ADD COLUMN meeting_seat_number VARCHAR(50);

COMMENT ON COLUMN blade_user.employee_number IS '工号';
COMMENT ON COLUMN blade_user.room_number IS '房号';
COMMENT ON COLUMN blade_user.meeting_seat_number IS '会议座位号';

-- 2025-08-01 添加钉钉用户表
DROP TABLE IF EXISTS "public"."ms_dingapp_user";
CREATE TABLE "public"."ms_dingapp_user" (
                                            "id" int8 NOT NULL,
                                            "user_id" int8,
                                            "union_id" varchar(255) COLLATE "pg_catalog"."default",
                                            "create_user" int8,
                                            "create_dept" int8,
                                            "create_time" timestamp(6),
                                            "update_user" int8,
                                            "update_time" timestamp(6),
                                            "status" int4,
                                            "is_deleted" int4,
                                            "tenant_id" varchar(12) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ms_dingapp_user"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."ms_dingapp_user"."union_id" IS '小程序UNIONID';
COMMENT ON COLUMN "public"."ms_dingapp_user"."create_user" IS '创建人';
COMMENT ON COLUMN "public"."ms_dingapp_user"."create_dept" IS '创建部门';
COMMENT ON COLUMN "public"."ms_dingapp_user"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."ms_dingapp_user"."update_user" IS '修改人';
COMMENT ON COLUMN "public"."ms_dingapp_user"."update_time" IS '修改时间';
COMMENT ON COLUMN "public"."ms_dingapp_user"."status" IS '状态';
COMMENT ON COLUMN "public"."ms_dingapp_user"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "public"."ms_dingapp_user"."tenant_id" IS '租户ID';

-- ----------------------------
-- Primary Key structure for table ms_dingapp_user
-- ----------------------------
ALTER TABLE "public"."ms_dingapp_user" ADD CONSTRAINT "ms_dingapp_user_pkey" PRIMARY KEY ("id");


-- 2025-08-01 添加钉钉组织机构用户信息表
DROP TABLE IF EXISTS "public"."ms_ding_user";
CREATE TABLE "public"."ms_ding_user" (
                                         "id" int8 NOT NULL,
                                         "userid" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
                                         "name" varchar(100) COLLATE "pg_catalog"."default",
                                         "mobile" varchar(20) COLLATE "pg_catalog"."default",
                                         "title" varchar(100) COLLATE "pg_catalog"."default",
                                         "dept_id_list" text COLLATE "pg_catalog"."default",
                                         "dept_names" text COLLATE "pg_catalog"."default",
                                         "parent_dept_ids" text COLLATE "pg_catalog"."default",
                                         "create_time" timestamp(6),
                                         "update_time" timestamp(6),
                                         "status" int4 DEFAULT 1
)
;
COMMENT ON COLUMN "public"."ms_ding_user"."userid" IS '钉钉用户ID';
COMMENT ON COLUMN "public"."ms_ding_user"."name" IS '用户姓名';
COMMENT ON COLUMN "public"."ms_ding_user"."mobile" IS '手机号';
COMMENT ON COLUMN "public"."ms_ding_user"."title" IS '职位';
COMMENT ON COLUMN "public"."ms_ding_user"."dept_id_list" IS '部门ID列表（JSON格式）';
COMMENT ON COLUMN "public"."ms_ding_user"."dept_names" IS '部门名称列表（JSON格式）';
COMMENT ON COLUMN "public"."ms_ding_user"."parent_dept_ids" IS '父级部门ID列表（JSON格式）';
COMMENT ON COLUMN "public"."ms_ding_user"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."ms_ding_user"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."ms_ding_user"."status" IS '状态（1-正常，0-删除）';
COMMENT ON TABLE "public"."ms_ding_user" IS '组织机构用户信息';

-- ----------------------------
-- Indexes structure for table ms_ding_user
-- ----------------------------
CREATE INDEX "idx_create_time" ON "public"."ms_ding_user" USING btree (
    "create_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
    );
CREATE INDEX "idx_mobile" ON "public"."ms_ding_user" USING btree (
    "mobile" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "idx_name" ON "public"."ms_ding_user" USING btree (
    "name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );
CREATE INDEX "idx_status" ON "public"."ms_ding_user" USING btree (
    "status" "pg_catalog"."int4_ops" ASC NULLS LAST
    );
CREATE UNIQUE INDEX "uk_userid" ON "public"."ms_ding_user" USING btree (
    "userid" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
    );

-- ----------------------------
-- Primary Key structure for table ms_ding_user
-- ----------------------------
ALTER TABLE "public"."ms_ding_user" ADD CONSTRAINT "ms_ding_user_pkey" PRIMARY KEY ("id");

-- 2025-08-07 增加用户的最后登录时间
ALTER TABLE "public"."blade_user"
    ADD COLUMN "last_login_time" timestamp(6);

COMMENT ON COLUMN "public"."blade_user"."last_login_time" IS '最后登录时间';

-- 2025-08-07 修改签到表字段类型
ALTER TABLE hy_attendance_record ALTER COLUMN id TYPE BIGINT;
ALTER TABLE hy_attendance_record ALTER COLUMN user_id TYPE BIGINT;
ALTER TABLE hy_attendance_record ALTER COLUMN hy_agenda_id TYPE BIGINT;
--删除外键
ALTER TABLE hy_attendance_record DROP CONSTRAINT hy_attendance_record_user_id_fkey;
ALTER TABLE hy_attendance_record DROP CONSTRAINT hy_attendance_record_hy_agenda_id_fkey;

-- 2025-08-11 用餐管理表
-- 用于管理用户每日的用餐状态和二维码信息
DROP TABLE IF EXISTS "hy_dinner";
CREATE TABLE hy_dinner (
    id BIGINT PRIMARY KEY,                    -- 主键，自增
    dinner_date DATE NOT NULL,                -- 用餐日期
    user_id BIGINT NOT NULL,                  -- 用户ID，关联blade_user表
    breakfast_status INT DEFAULT 0,           -- 早餐状态（0-未用餐，1-已用餐）
    breakfast_qr_code VARCHAR(500),           -- 早餐二维码
    lunch_status INT DEFAULT 0,               -- 中餐状态（0-未用餐，1-已用餐）
    lunch_qr_code VARCHAR(500),               -- 中餐二维码
    dinner_status INT DEFAULT 0,              -- 晚餐状态（0-未用餐，1-已用餐）
    dinner_qr_code VARCHAR(500),              -- 晚餐二维码
    create_user BIGINT,                       -- 创建人
    create_dept BIGINT,                       -- 创建部门
    create_time TIMESTAMP(6),                 -- 创建时间
    update_user BIGINT,                       -- 更新人
    update_time TIMESTAMP(6),                 -- 更新时间
    status INT DEFAULT 1,                     -- 状态
    is_deleted INT DEFAULT 0                  -- 是否删除
);

-- 添加表注释
COMMENT ON TABLE hy_dinner IS '用餐管理表';
COMMENT ON COLUMN hy_dinner.id IS '主键，自增';
COMMENT ON COLUMN hy_dinner.dinner_date IS '用餐日期';
COMMENT ON COLUMN hy_dinner.user_id IS '用户ID，关联blade_user表';
COMMENT ON COLUMN hy_dinner.breakfast_status IS '早餐状态（0-未用餐，1-已用餐）';
COMMENT ON COLUMN hy_dinner.breakfast_qr_code IS '早餐二维码';
COMMENT ON COLUMN hy_dinner.lunch_status IS '中餐状态（0-未用餐，1-已用餐）';
COMMENT ON COLUMN hy_dinner.lunch_qr_code IS '中餐二维码';
COMMENT ON COLUMN hy_dinner.dinner_status IS '晚餐状态（0-未用餐，1-已用餐）';
COMMENT ON COLUMN hy_dinner.dinner_qr_code IS '晚餐二维码';
COMMENT ON COLUMN hy_dinner.create_user IS '创建人';
COMMENT ON COLUMN hy_dinner.create_dept IS '创建部门';
COMMENT ON COLUMN hy_dinner.create_time IS '创建时间';
COMMENT ON COLUMN hy_dinner.update_user IS '更新人';
COMMENT ON COLUMN hy_dinner.update_time IS '更新时间';
COMMENT ON COLUMN hy_dinner.status IS '状态';
COMMENT ON COLUMN hy_dinner.is_deleted IS '是否删除';

-- 创建索引
CREATE INDEX idx_hy_dinner_user_id ON hy_dinner(user_id);
CREATE INDEX idx_hy_dinner_date ON hy_dinner(dinner_date);
CREATE INDEX idx_hy_dinner_status ON hy_dinner(status);
CREATE INDEX idx_hy_dinner_is_deleted ON hy_dinner(is_deleted);
CREATE UNIQUE INDEX uk_hy_dinner_user_date ON hy_dinner(user_id, dinner_date) WHERE is_deleted = 0;

-- 2025-08-11 酒店信息表
-- 用于管理用户的酒店住宿信息，与用户表1对1关系
DROP TABLE IF EXISTS "hy_hotel";
CREATE TABLE hy_hotel (
    id BIGINT PRIMARY KEY,                    -- 主键，自增
    hotel_name VARCHAR(200),                  -- 酒店名称
    hotel_rating INT,                         -- 酒店星级
    hotel_address VARCHAR(500),               -- 酒店地址
    distance_to_venue VARCHAR(200),           -- 距离会场
    hotel_phone VARCHAR(50),                  -- 酒店电话
    room_type VARCHAR(100),                   -- 房间类型
    room_features TEXT,                       -- 房间设施（JSON格式）
    checkin_time TIMESTAMP(6),                -- 入住时间
    checkout_time TIMESTAMP(6),               -- 退房时间
    stay_days INT,                            -- 住宿天数
    hotel_services TEXT,                      -- 酒店服务（JSON格式）
    transport_info TEXT,                      -- 交通信息（JSON格式）
    contact_info TEXT,                        -- 联系方式（JSON格式）
    hotel_tips TEXT,                          -- 酒店提示（JSON格式）
    create_user BIGINT,                       -- 创建人
    create_dept BIGINT,                       -- 创建部门
    create_time TIMESTAMP(6),                 -- 创建时间
    update_user BIGINT,                       -- 更新人
    update_time TIMESTAMP(6),                 -- 更新时间
    status INT DEFAULT 1,                     -- 状态
    is_deleted INT DEFAULT 0                  -- 是否删除
);

-- 添加表注释
COMMENT ON TABLE hy_hotel IS '酒店信息表';
COMMENT ON COLUMN hy_hotel.id IS '主键，自增';
COMMENT ON COLUMN hy_hotel.hotel_name IS '酒店名称';
COMMENT ON COLUMN hy_hotel.hotel_rating IS '酒店星级';
COMMENT ON COLUMN hy_hotel.hotel_address IS '酒店地址';
COMMENT ON COLUMN hy_hotel.distance_to_venue IS '距离会场';
COMMENT ON COLUMN hy_hotel.hotel_phone IS '酒店电话';
COMMENT ON COLUMN hy_hotel.room_type IS '房间类型';
COMMENT ON COLUMN hy_hotel.room_features IS '房间设施（JSON格式）';
COMMENT ON COLUMN hy_hotel.checkin_time IS '入住时间';
COMMENT ON COLUMN hy_hotel.checkout_time IS '退房时间';
COMMENT ON COLUMN hy_hotel.stay_days IS '住宿天数';
COMMENT ON COLUMN hy_hotel.hotel_services IS '酒店服务（JSON格式）';
COMMENT ON COLUMN hy_hotel.transport_info IS '交通信息（JSON格式）';
COMMENT ON COLUMN hy_hotel.contact_info IS '联系方式（JSON格式）';
COMMENT ON COLUMN hy_hotel.hotel_tips IS '酒店提示（JSON格式）';
COMMENT ON COLUMN hy_hotel.create_user IS '创建人';
COMMENT ON COLUMN hy_hotel.create_dept IS '创建部门';
COMMENT ON COLUMN hy_hotel.create_time IS '创建时间';
COMMENT ON COLUMN hy_hotel.update_user IS '更新人';
COMMENT ON COLUMN hy_hotel.update_time IS '更新时间';
COMMENT ON COLUMN hy_hotel.status IS '状态';
COMMENT ON COLUMN hy_hotel.is_deleted IS '是否删除';

-- 创建索引
CREATE UNIQUE INDEX uk_hy_hotel_user_id ON hy_hotel(user_id) WHERE is_deleted = 0;
CREATE INDEX idx_hy_hotel_status ON hy_hotel(status);
CREATE INDEX idx_hy_hotel_is_deleted ON hy_hotel(is_deleted);
CREATE INDEX idx_hy_hotel_name ON hy_hotel(hotel_name);

-- 2025-08-11 用户会议关联表
-- 用于管理用户与会议议程的关联关系，1对多
DROP TABLE IF EXISTS "hy_user_agenda";
CREATE TABLE hy_user_agenda (
    id BIGINT PRIMARY KEY,                    -- 主键，自增
    user_id BIGINT NOT NULL,                  -- 用户ID，关联blade_user表
    agenda_id BIGINT NOT NULL,                -- 议程ID，关联hy_agenda表
    agenda_date DATE NOT NULL,                -- 会议日期
    start_time TIMESTAMP(6),                  -- 开始时间
    end_time TIMESTAMP(6),                    -- 结束时间
    topic VARCHAR(500),                       -- 会议主题
    comment VARCHAR(1000),                    -- 备注信息（如地点等）
    day_number INT,                           -- 第几天（后台录入，前端不显示）
    create_user BIGINT,                       -- 创建人
    create_dept BIGINT,                       -- 创建部门
    create_time TIMESTAMP(6),                 -- 创建时间
    update_user BIGINT,                       -- 更新人
    update_time TIMESTAMP(6),                 -- 更新时间
    status INT DEFAULT 1,                     -- 状态
    is_deleted INT DEFAULT 0                  -- 是否删除
);

-- 添加表注释
COMMENT ON TABLE hy_user_agenda IS '用户会议关联表';
COMMENT ON COLUMN hy_user_agenda.id IS '主键，自增';
COMMENT ON COLUMN hy_user_agenda.user_id IS '用户ID，关联blade_user表';
COMMENT ON COLUMN hy_user_agenda.agenda_id IS '议程ID，关联hy_agenda表';
COMMENT ON COLUMN hy_user_agenda.agenda_date IS '会议日期';
COMMENT ON COLUMN hy_user_agenda.start_time IS '开始时间';
COMMENT ON COLUMN hy_user_agenda.end_time IS '结束时间';
COMMENT ON COLUMN hy_user_agenda.topic IS '会议主题';
COMMENT ON COLUMN hy_user_agenda.comment IS '备注信息（如地点等）';
COMMENT ON COLUMN hy_user_agenda.content IS '完整内容（topic+comment）';
COMMENT ON COLUMN hy_user_agenda.day_number IS '第几天（后台录入，前端不显示）';
COMMENT ON COLUMN hy_user_agenda.create_user IS '创建人';
COMMENT ON COLUMN hy_user_agenda.create_dept IS '创建部门';
COMMENT ON COLUMN hy_user_agenda.create_time IS '创建时间';
COMMENT ON COLUMN hy_user_agenda.update_user IS '更新人';
COMMENT ON COLUMN hy_user_agenda.update_time IS '更新时间';
COMMENT ON COLUMN hy_user_agenda.status IS '状态';
COMMENT ON COLUMN hy_user_agenda.is_deleted IS '是否删除';

-- 创建索引
CREATE INDEX idx_hy_user_agenda_user_id ON hy_user_agenda(user_id);
CREATE INDEX idx_hy_user_agenda_agenda_id ON hy_user_agenda(agenda_id);
CREATE INDEX idx_hy_user_agenda_date ON hy_user_agenda(agenda_date);
CREATE INDEX idx_hy_user_agenda_day_number ON hy_user_agenda(day_number);
CREATE INDEX idx_hy_user_agenda_status ON hy_user_agenda(status);
CREATE INDEX idx_hy_user_agenda_is_deleted ON hy_user_agenda(is_deleted);
CREATE INDEX idx_hy_user_agenda_time ON hy_user_agenda(start_time, end_time);


