<template>
  <div class="page-container">
    <!-- 页面内容 -->
    <main class="page-content">
      <div class="list-container">
        <div class="form-header">
          <i class="fas fa-calendar-check"></i>
          <h2>我的日程</h2>
          <p>个人会议安排</p>
        </div>

        <!-- 日程内容滚动容器 -->
        <div class="schedule-scroll-container">
          <!-- 日程内容 -->
          <div class="schedule-content">
            <div class="content-card list-item">
              <div class="rich-text-content" v-html="scheduleContent"></div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
export default {
  name: 'MySchedule',
  data() {
    return {
      loading: true,
      scheduleContent: '',
      userInfo: null,
      // 默认日程内容（作为示例）
      defaultScheduleContent: `
        <div class="schedule-section">
          <h3>📅 会议日程安排</h3>
          <div class="day-schedule">
            <h4>第一天 - 2025年9月15日（周一）</h4>
            <div class="time-slot">
              <div class="time">08:30-09:00</div>
              <div class="event">签到入场</div>
              <div class="location">主会场大厅</div>
            </div>
            <div class="time-slot">
              <div class="time">09:00-09:30</div>
              <div class="event">开幕式</div>
              <div class="location">主会场A厅</div>
            </div>
            <div class="time-slot">
              <div class="time">09:30-11:00</div>
              <div class="event">主题演讲：企业数字化转型</div>
              <div class="location">主会场A厅</div>
            </div>
            <div class="time-slot">
              <div class="time">11:00-11:15</div>
              <div class="event">茶歇</div>
              <div class="location">休息区</div>
            </div>
            <div class="time-slot">
              <div class="time">11:15-12:30</div>
              <div class="event">管理创新实践分享</div>
              <div class="location">主会场A厅</div>
            </div>
            <div class="time-slot">
              <div class="time">12:30-14:00</div>
              <div class="event">午餐时间</div>
              <div class="location">1楼餐厅</div>
            </div>
            <div class="time-slot">
              <div class="time">14:00-16:00</div>
              <div class="event">分组讨论：智能制造</div>
              <div class="location">分会场B厅</div>
            </div>
            <div class="time-slot">
              <div class="time">16:00-16:15</div>
              <div class="event">茶歇</div>
              <div class="location">休息区</div>
            </div>
            <div class="time-slot">
              <div class="time">16:15-17:30</div>
              <div class="event">经验交流与讨论</div>
              <div class="location">主会场A厅</div>
            </div>
            <div class="time-slot">
              <div class="time">18:00-20:00</div>
              <div class="event">欢迎晚宴</div>
              <div class="location">2楼宴会厅</div>
            </div>
          </div>

          <div class="day-schedule">
            <h4>第二天 - 2025年9月16日（周二）</h4>
            <div class="time-slot">
              <div class="time">09:00-10:30</div>
              <div class="event">人工智能在企业管理中的应用</div>
              <div class="location">主会场A厅</div>
            </div>
            <div class="time-slot">
              <div class="time">10:30-10:45</div>
              <div class="event">茶歇</div>
              <div class="location">休息区</div>
            </div>
            <div class="time-slot">
              <div class="time">10:45-12:00</div>
              <div class="event">圆桌会议：未来发展趋势</div>
              <div class="location">圆桌会议室</div>
            </div>
            <div class="time-slot">
              <div class="time">12:00-13:30</div>
              <div class="event">午餐时间</div>
              <div class="location">1楼餐厅</div>
            </div>
            <div class="time-slot">
              <div class="time">13:30-15:00</div>
              <div class="event">案例分析与讨论</div>
              <div class="location">分会场C厅</div>
            </div>
            <div class="time-slot">
              <div class="time">15:00-16:30</div>
              <div class="event">技术展示与体验</div>
              <div class="location">展示区</div>
            </div>
            <div class="time-slot">
              <div class="time">16:30-17:00</div>
              <div class="event">自由交流</div>
              <div class="location">交流区</div>
            </div>
          </div>

          <div class="day-schedule">
            <h4>第三天 - 2025年9月17日（周三）</h4>
            <div class="time-slot">
              <div class="time">09:00-10:00</div>
              <div class="event">总结大会</div>
              <div class="location">主会场A厅</div>
            </div>
            <div class="time-slot">
              <div class="time">10:00-10:30</div>
              <div class="event">颁奖典礼</div>
              <div class="location">主会场A厅</div>
            </div>
            <div class="time-slot">
              <div class="time">10:30-11:00</div>
              <div class="event">闭幕式</div>
              <div class="location">主会场A厅</div>
            </div>
            <div class="time-slot">
              <div class="time">11:00-12:00</div>
              <div class="event">合影留念</div>
              <div class="location">主会场大厅</div>
            </div>
          </div>
        </div>

        <div class="schedule-section">
          <h3>📋 重要提醒</h3>
          <ul class="reminder-list">
            <li><strong>签到时间：</strong>每日08:30开始，请提前到达</li>
            <li><strong>着装要求：</strong>商务正装，佩戴会议胸牌</li>
            <li><strong>手机设置：</strong>会议期间请将手机调至静音模式</li>
            <li><strong>资料获取：</strong>会议资料可在会务助手中下载</li>
            <li><strong>网络连接：</strong>会场提供免费WiFi，密码：Conference2025</li>
          </ul>
        </div>

        <div class="schedule-section">
          <h3>📍 会场信息</h3>
          <div class="venue-info">
            <p><strong>主会场A厅：</strong>可容纳500人，配备同声传译设备</p>
            <p><strong>分会场B厅：</strong>可容纳200人，适合分组讨论</p>
            <p><strong>分会场C厅：</strong>可容纳150人，配备投影设备</p>
            <p><strong>圆桌会议室：</strong>可容纳50人，高端商务环境</p>
            <p><strong>展示区：</strong>技术产品展示，互动体验</p>
            <p><strong>休息区：</strong>提供茶歇服务，网络休闲区</p>
          </div>
        </div>

        <div class="schedule-section">
          <h3>🎯 参会须知</h3>
          <ul class="notice-list">
            <li>请按时参加各项议程，迟到可能影响入场</li>
            <li>会议期间禁止录音录像，如有需要请联系工作人员</li>
            <li>用餐时间请有序排队，文明用餐</li>
            <li>如有特殊需求或紧急情况，请联系现场工作人员</li>
            <li>会议结束后请带走个人物品，保持会场整洁</li>
          </ul>
        </div>
      `
    };
  },
  mounted() {
    this.loadUserSchedule();
  },
  methods: {
    // 加载用户日程信息
    async loadUserSchedule() {
      try {
        this.loading = true;
        const response = await getCurrentUserSchedule();

        if (response.data.success && response.data.data) {
          const scheduleData = response.data.data;

          // 设置用户信息
          this.userInfo = {
            userName: scheduleData.userName,
            userRealName: scheduleData.userRealName,
            userEmail: scheduleData.userEmail,
            userPhone: scheduleData.userPhone
          };

          // 设置日程内容
          this.scheduleContent = scheduleData.scheduleContent || this.defaultScheduleContent;
        } else {
          // 没有日程数据时使用默认内容
          this.scheduleContent = this.defaultScheduleContent;
        }
      } catch (error) {
        console.error('加载用户日程失败:', error);
        // 加载失败时使用默认内容
        this.scheduleContent = this.defaultScheduleContent;
        this.$message.warning('加载日程信息失败，显示默认内容');
      } finally {
        this.loading = false;
      }
    }
  }
}
</script>

<style scoped>
/* 页面容器 */
.page-container {
  width: 100%;
  max-height: 85vh;
  background-size: cover;
  position: relative;
  z-index: 1;
  margin: 0 auto;
  box-sizing: border-box;
}

/* 页面内容 */
.page-content {
  margin-top: 20px;
}

/* 容器样式 - 磨砂玻璃效果 */
.list-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(12px);
  border-radius: 15px;
  padding: 10px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: inset 0 0 10px rgba(255, 255, 255, 0.05),
  0 10px 25px rgba(0, 0, 0, 0.3);
  animation: slideInUp 0.6s ease forwards;
  max-width: 1400px;
  margin: 0 auto;
}

/* 标题区域样式 */
.form-header {
  text-align: center;
  margin-bottom: 25px;
  color: #ffffff;
}

.form-header i {
  font-size: 36px;
  color: #07D3F0;
  margin-bottom: 10px;
  text-shadow: 0 0 15px rgba(7, 211, 240, 0.6);
}

.form-header h2 {
  color: #ffffff;
  font-size: 24px;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.form-header p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  padding: 0 10px;
}

/* 日程内容滚动容器 */
.schedule-scroll-container {
  max-height: calc(85vh - 200px);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding-right: 5px;
}

/* 滚动条美化 */
.schedule-scroll-container::-webkit-scrollbar {
  width: 5px;
}

.schedule-scroll-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
}

.schedule-scroll-container::-webkit-scrollbar-thumb {
  background: rgba(7, 211, 240, 0.5);
  border-radius: 10px;
}

.schedule-scroll-container::-webkit-scrollbar-thumb:hover {
  background: rgba(7, 211, 240, 0.8);
}

/* 列表项样式 - 卡片风格 */
.list-item {
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-left: 4px solid rgba(7, 211, 240, 0.7);
  transition: all 0.3s ease;
  animation: slideInUp 0.6s ease forwards;
  position: relative;
  overflow: hidden;
}

/* 卡片悬停效果 */
.list-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(
      120deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.list-item:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.list-item:hover {
  transform: translateX(3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.09);
}

/* 日程内容 */
.schedule-content {
  margin: 30px 0;
}

.content-card {
  padding: 25px;
}

/* 富文本内容样式 */
.rich-text-content {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
}

.rich-text-content .schedule-section {
  margin-bottom: 30px;
}

.rich-text-content .schedule-section:last-child {
  margin-bottom: 0;
}

.rich-text-content h3 {
  color: #07D3F0;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid rgba(7, 211, 240, 0.7);
  text-shadow: 0 0 10px rgba(7, 211, 240, 0.3);
}

.rich-text-content h4 {
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
  margin: 20px 0 15px 0;
  padding: 10px;
  background: rgba(7, 211, 240, 0.1);
  border-radius: 8px;
  border-left: 4px solid #07D3F0;
}

.rich-text-content .day-schedule {
  margin-bottom: 25px;
}

.rich-text-content .time-slot {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border-left: 3px solid #07D3F0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.rich-text-content .time-slot::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(
      120deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.1) 50%,
      rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.rich-text-content .time-slot:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.rich-text-content .time-slot:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(5px);
}

.rich-text-content .time {
  font-weight: 600;
  color: #07D3F0;
  min-width: 120px;
  font-size: 14px;
  text-shadow: 0 0 10px rgba(7, 211, 240, 0.3);
}

.rich-text-content .event {
  flex: 1;
  font-weight: 500;
  color: #ffffff;
  margin: 0 15px;
  font-size: 14px;
}

.rich-text-content .location {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  min-width: 100px;
  text-align: right;
}

.rich-text-content ul {
  list-style: none;
  padding: 0;
}

.rich-text-content li {
  padding: 10px;
  margin-bottom: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border-left: 3px solid #07D3F0;
  font-size: 14px;
  line-height: 1.5;
  position: relative;
  overflow: hidden;
}

.rich-text-content li::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(
      120deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.1) 50%,
      rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.rich-text-content li:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.rich-text-content .venue-info p {
  padding: 10px;
  margin-bottom: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border-left: 3px solid #07D3F0;
  font-size: 14px;
  line-height: 1.5;
  position: relative;
  overflow: hidden;
}

.rich-text-content .venue-info p::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(
      120deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.1) 50%,
      rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.rich-text-content .venue-info p:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.rich-text-content strong {
  color: #07D3F0;
  font-weight: 600;
  text-shadow: 0 0 10px rgba(7, 211, 240, 0.3);
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .list-container {
    padding: 15px;
  }

  .content-card {
    padding: 20px;
  }

  .form-header i {
    font-size: 40px;
  }

  .form-header h2 {
    font-size: 20px;
  }

  .rich-text-content .time-slot {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .rich-text-content .time,
  .rich-text-content .location {
    min-width: auto;
    text-align: left;
  }
}
</style>
