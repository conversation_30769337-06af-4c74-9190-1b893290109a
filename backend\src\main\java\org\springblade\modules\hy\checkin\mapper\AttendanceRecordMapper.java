/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.hy.checkin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springblade.modules.hy.checkin.pojo.entity.AttendanceRecordEntity;
import org.springblade.modules.hy.checkin.pojo.vo.AttendanceRecordVO;

import java.util.List;

/**
 * 参会签到记录表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
public interface AttendanceRecordMapper extends BaseMapper<AttendanceRecordEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page             分页对象
	 * @param attendanceRecord 查询条件
	 * @return 分页结果
	 */
	List<AttendanceRecordVO> selectAttendanceRecordPage(IPage<AttendanceRecordVO> page, AttendanceRecordVO attendanceRecord);

	/**
	 * 根据用户ID查询今日签到记录
	 *
	 * @param userId 用户ID
	 * @return 签到记录
	 */
	AttendanceRecordEntity selectTodayRecordByUserId(@Param("userId") Long userId);

	/**
	 * 根据用户ID和议程ID查询签到记录
	 *
	 * @param userId    用户ID
	 * @param agendaId  议程ID
	 * @return 签到记录
	 */
	AttendanceRecordEntity selectByUserIdAndAgendaId(@Param("userId") Long userId, @Param("agendaId") Long agendaId);

}
