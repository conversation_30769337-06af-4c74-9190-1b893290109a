/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.hy.checkin.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 签到请求 DTO
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
@Data
@Schema(description = "签到请求对象")
public class CheckinRequestDTO implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 签到码
	 */
	@NotBlank(message = "签到码不能为空")
	@Schema(description = "签到码", required = true)
	private String checkinCode;

	/**
	 * 议程ID（必填）
	 */
	@Schema(description = "议程ID", required = true)
	private Integer agendaId;

}
