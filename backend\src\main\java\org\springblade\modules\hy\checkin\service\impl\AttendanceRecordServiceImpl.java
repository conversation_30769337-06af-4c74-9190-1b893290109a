/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.modules.hy.checkin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.hy.checkin.mapper.AttendanceRecordMapper;
import org.springblade.modules.hy.checkin.pojo.dto.CheckinRequestDTO;
import org.springblade.modules.hy.checkin.pojo.dto.QRCodeResponseDTO;
import org.springblade.modules.hy.checkin.pojo.entity.AttendanceRecordEntity;
import org.springblade.modules.hy.checkin.pojo.vo.AttendanceRecordVO;
import org.springblade.modules.hy.checkin.service.IAttendanceRecordService;
import org.springblade.modules.hy.checkin.utils.QRCodeUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 参会签到记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
@Slf4j
@Service
public class AttendanceRecordServiceImpl extends BaseServiceImpl<AttendanceRecordMapper, AttendanceRecordEntity> implements IAttendanceRecordService {

	/**
	 * 移动端签到页面基础URL
	 */
	@Value("${ding-app.qrcode.mobile-base-url}")
	private String mobileBaseUrl;

	@Override
	public IPage<AttendanceRecordVO> selectAttendanceRecordPage(IPage<AttendanceRecordVO> page, AttendanceRecordVO attendanceRecord) {
		return page.setRecords(baseMapper.selectAttendanceRecordPage(page, attendanceRecord));
	}

	@Override
	public QRCodeResponseDTO generateAgendaQRCode(Long agendaId) {
		// 生成议程签到码（不包含用户ID）
		String checkinCode = QRCodeUtil.generateAgendaCheckinCode(agendaId);

		// 生成二维码内容（包含跳转URL）
		String qrContent = String.format("%s/mobile/checkin?agendaId=%d", mobileBaseUrl, agendaId);
		String qrCodeData = QRCodeUtil.generateQRCodeBase64(qrContent);

		// 设置过期时间（24小时后，议程级别的二维码有效期更长）
		LocalDateTime expireTime = LocalDateTime.now().plusHours(24);

		// 构建响应
		QRCodeResponseDTO response = new QRCodeResponseDTO();
		response.setQrCodeData(qrCodeData);
		response.setCheckinCode(checkinCode);
		response.setExpireTime(expireTime);
		response.setUserId(null); // 议程级别二维码不绑定用户
		response.setAgendaId(agendaId);

		log.info("为议程 {} 生成签到二维码，签到码：{}", agendaId, checkinCode);

		return response;
	}

	@Override
	public AttendanceRecordEntity checkinByAgenda(Long agendaId, Long userId) {
		// 检查是否已经签到
		if (isCheckedIn(userId, agendaId)) {
			return null; // 返回null表示已经签到过了
		}

		// 创建签到记录
		AttendanceRecordEntity record = new AttendanceRecordEntity();
		record.setUserId(userId);
		record.setHyAgendaId(agendaId);  // 转换为Long存储
		record.setCheckinTime(LocalDateTime.now());
		record.setStatusText("已签到");

		// 保存签到记录
		boolean saved = save(record);
		if (!saved) {
			return null; // 返回null表示保存失败
		}

		log.info("用户 {} 议程 {} 签到成功", userId, agendaId);

		return record;
	}

	@Override
	public AttendanceRecordEntity getCheckinStatus(Long userId, Long agendaId) {
		if (agendaId != null) {
			// 查询特定议程的签到记录
			return baseMapper.selectByUserIdAndAgendaId(userId, agendaId);
		} else {
			// 查询今日签到记录
			return baseMapper.selectTodayRecordByUserId(userId);  // 直接使用Long类型
		}
	}

	@Override
	public boolean isCheckedIn(Long userId, Long agendaId) {
		LambdaQueryWrapper<AttendanceRecordEntity> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(AttendanceRecordEntity::getUserId, userId);  // 直接使用Long类型，不转换

		if (agendaId != null) {
			// 检查特定议程的签到状态
			wrapper.eq(AttendanceRecordEntity::getHyAgendaId, agendaId);
		} else {
			// 检查今日签到状态
			LocalDate today = LocalDate.now();
			wrapper.ge(AttendanceRecordEntity::getCheckinTime, today.atStartOfDay());
			wrapper.lt(AttendanceRecordEntity::getCheckinTime, today.plusDays(1).atStartOfDay());
		}

		return count(wrapper) > 0;
	}

}
